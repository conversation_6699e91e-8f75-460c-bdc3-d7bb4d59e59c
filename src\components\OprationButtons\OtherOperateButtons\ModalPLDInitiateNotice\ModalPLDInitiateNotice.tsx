import I18N from '@/utils/I18N';
import { message } from '@dzg/common-utils';
import { DzgForm } from '@dzg/dzg-form';
import { Button, Modal, Popconfirm, Tabs } from 'antd';
import Form from '@/components/EnhanceForm';
import React, { useImperativeHandle, useRef, useState } from 'react';
import { HBLInfo } from './HBLInfo';
import { arrayFilterBoolean } from '@/utils';
import { tipsUtils } from '@/utils-business';
import { apiChangeOrderNoticeDetail, apiChangeOrderSave, apiReBillExchange, TypePLDNoticeDetail } from './pldNoticeApi';
import { OssFileUploader } from '@dzg/base-component';
import { FormItemValue } from '@/components/FormItemValue';
import { PLDNoticeWatchProvider, calcTabErrorNode } from './pldNoticeUtils';
import { RedoOutlined } from '@ant-design/icons';

type TypeProps = {
  /** 组件Props定义 */
};

type TypeOpenParams = {
  /** 组件打开参数定义 */
  /** 订单ID */
  orderId?: string;
};

type TypeOperateMap = {
  /** 提交成功后回调 */
  submitSuccessCB?: () => void;
};

export type TypeModalRef = {
  open: (params: TypeOpenParams, operate?: TypeOperateMap) => Promise<any>;
};

/** 湃乐多发起换单通知组件 */
export default React.forwardRef(function ModalPLDInitiateNotice(
  props: TypeProps,
  ref: React.Ref<TypeModalRef | undefined>
) {
  const [loading, setLoading] = useState(false);
  const {
    modalVisible,
    form,
    asyncInit,
    asyncClear,
    asyncSubmit,
    setModalVisible,
    tabKey,
    setTabKey,
    asyncReset,
  } = useConfig();
  const feePaymentMethod = Form.useWatch('feePaymentMethod', form);

  useImperativeHandle(ref, () => ({
    async open(params: TypeOpenParams, operate?: TypeOperateMap) {
      asyncInit(params, operate);
    },
  }));
  return (
    <Modal
      {...{
        title: (
          <div>
            <span>{I18N.Src__Components__OprationButtons__OtherOperateButtons.Index.initiateAReplacementOrder}</span>
            <Popconfirm
              {...{
                title:
                  I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                    .ModalPLDInitiateNotice.confirmReset,
                onConfirm: asyncReset,
                children: (
                  <span style={{ marginLeft: 8, cursor: 'pointer' }}>
                    <RedoOutlined />
                  </span>
                ),
              }}
            />
          </div>
        ),
        width: 500,
        visible: modalVisible,
        closable: loading === false,
        maskClosable: false,
        cancelButtonProps: {
          disabled: loading,
        },
        okButtonProps: {
          loading,
        },
        onCancel() {
          setModalVisible(false);
        },
        afterClose() {
          asyncClear();
        },
        footer: (
          <>
            <Button onClick={() => setModalVisible(false)}>
              {
                I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                  .ModalPLDInitiateNotice.cancel
              }
            </Button>
            <Button
              onClick={async () => {
                setLoading(true);
                await asyncSubmit({ isSubmit: false }).finally(() => setLoading(false));
                setModalVisible(false);
              }}
            >
              {
                I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                  .ModalPLDInitiateNotice.preservation
              }
            </Button>
            <Button
              type="primary"
              onClick={async () => {
                setLoading(true);
                await asyncSubmit({ isSubmit: true }).finally(() => setLoading(false));
                setModalVisible(false);
              }}
            >
              {
                I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                  .ModalPLDInitiateNotice.saveAndSubmit
              }
            </Button>
          </>
        ),
      }}
    >
      <DzgForm
        {...{
          layout: 'horizontal',
          form,
          size: 'small',
          //   labelAlign: 'left',
          labelWidth: 7,
          schema: arrayFilterBoolean([
            {
              formItemProps: {
                name: 'hidden',
                hidden: true,
              },
              render() {
                return (
                  <>
                    <FormItemValue
                      {...{
                        name: 'orderId',
                        label:
                          I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDChangeOrder
                            .ModalPLDChangeOrder.order,
                      }}
                    />
                    <FormItemValue
                      {...{
                        name: 'id',
                        label:
                          I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                            .ModalPLDInitiateNotice.details,
                      }}
                    />
                    <PLDNoticeWatchProvider fnParams={[{ form }]} />
                  </>
                );
              },
            },
            {
              formItemProps: {
                name: 'feePaymentMethod',
                label:
                  I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                    .ModalPLDInitiateNotice.exchangeFeePayment,
                rules: [{ required: true }],
              },
              tag: 'Radio.Group',
              tagProps: {
                options: [
                  {
                    label:
                      I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                        .ModalPLDInitiateNotice.paymentInAdvance,
                    value: 'PREPAID',
                  },
                  {
                    label:
                      I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                        .ModalPLDInitiateNotice.collectOnDelivery,
                    value: 'COLLECT',
                  },
                ],
              },
            },
            (feePaymentMethod === 'COLLECT' && {
              formItemProps: {
                name: 'feeCollectAmount',
                label:
                  I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                    .ModalPLDInitiateNotice.collectAndExchangeDocuments,
                rules: [
                  { required: true },
                  {
                    async validator(rule: any, value: any) {
                      if (value !== '' && Number(value) <= 0) {
                        throw new Error(
                          I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.ModalPLDInitiateNotice.mustBeGreaterThan
                        );
                      }
                    },
                  },
                ],
              },
              tagProps: {
                style: {
                  width: '100%',
                },
                precision: 4,
              },
              tag: 'InputNumber',
            }) as any,
            {
              formItemProps: {
                name: 'mblNo',
                label: 'MBL NO',
                rules: [{ required: true }, { max: 16 }],
              },
              tag: 'Input',
            },
            {
              formItemProps: {
                name: 'mblFileList',
                label:
                  I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                    .ModalPLDInitiateNotice.singleFile,
                rules: [{ required: true }],
                valuePropName: 'fileList',
              },
              tagProps: {},
              render() {
                return (
                  <OssFileUploader
                    {...{
                      maxFileSizeM: 100,
                      maxFileCount: 1,
                      isDragger: false,
                      className: 'oss-file-uploader',
                      enableSuffixList: '.doc,.docx,.pdf,.xls,.xlsx,.jpg,.jpeg,.png',
                      onChange(fileList) {
                        (fileList || []).forEach((item: any) => {
                          if (item.status === 'done') {
                            item.fileName = item.name;
                            item.fileUrl = item.url;
                            item.fileType = 'MBL';
                            item.fileSize = item.size;
                          }
                        });
                      },
                    }}
                  />
                );
              },
            },
            {
              formItemProps: {
                name: 'hblInfo',
                label:
                  I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice
                    .ModalPLDInitiateNotice.singleInformation,
                required: true,
                labelAlign: 'left',
              },
              render() {},
            },
            {
              formItemProps: {
                name: 'hblInfo',
                noStyle: true,
              },
              render() {
                return (
                  <Form.List {...{ name: 'hblList' }}>
                    {function () {
                      const hblList = (form.getFieldValue('hblList') as TypePLDNoticeDetail['hblList']) || [];
                      return (
                        <Tabs
                          {...{
                            activeKey: tabKey,
                            onChange: setTabKey,
                          }}
                        >
                          {hblList.map((hblInfo, index) => {
                            return (
                              <Tabs.TabPane
                                {...{
                                  key: index,
                                  tab: (
                                    <>
                                      {calcTabErrorNode({ key: index })}
                                      {hblInfo.hblNo}
                                    </>
                                  ),
                                  forceRender: true,
                                  children: <HBLInfo {...{ namePathPrefix: index, form }} />,
                                }}
                              />
                            );
                          })}
                        </Tabs>
                      );
                    }}
                  </Form.List>
                );
              },
            },
          ]),
        }}
      />
    </Modal>
  );
});

function useConfig() {
  let [modalVisible, setModalVisible] = useState(false);
  const [tabKey, setTabKey] = useState('0');
  const [form] = Form.useForm();
  const [detailData, setDetailData] = useState<any>({});
  const [operateMap, setOperateMap] = useState<TypeOperateMap>({});
  const refreshRef = useRef<() => Promise<TypePLDNoticeDetail>>();

  async function asyncInit(params: TypeOpenParams, operate?: TypeOperateMap) {
    const initData = transDetail(params);

    setDetailData(initData);
    form.setFieldsValue(initData);
    setOperateMap(operate || {});
    setModalVisible(true);

    refreshRef.current = async function asyncRefreshDetail() {
      const data = await apiChangeOrderNoticeDetail({ orderId: initData.orderId });
      setDetailData(transDetail(data));
      form.setFieldsValue(data);
      return data;
    };
    await refreshRef.current?.();
  }

  /** 重置表单 */
  async function asyncReset() {
    const id = form.getFieldValue('id');
    setTabKey('0');
    setDetailData({});
    setOperateMap({});
    form.resetFields();
    if (id) {
      await apiReBillExchange(id);
    }
    await refreshRef.current?.();
  }

  async function asyncClear() {
    setTabKey('0');
    setDetailData({});
    setOperateMap({});
    refreshRef.current = undefined;
    form.resetFields();
  }

  async function asyncSubmit(params: { isSubmit: boolean }) {
    const { isSubmit } = params;
    try {
      if (isSubmit) {
        await form.validateFields();
      }
    } catch (err) {
      message.error(tipsUtils.TIPS_FORM_VALIDATE_ERROR);
      console.error(err);
      form.setFields([{ name: 'listErrors', value: err }]);
      return Promise.reject();
    }
    const formData = form.getFieldsValue();

    await apiChangeOrderSave({ ...formData, isSubmit });
    message.success(isSubmit ? tipsUtils.TIPS_FORM_SUBMIT_SUCCESS : tipsUtils.TIPS_FORM_SAVE_SUCCESS);
    operateMap?.submitSuccessCB?.();
  }

  return {
    detailData,
    asyncInit,
    asyncClear,
    asyncSubmit,
    setModalVisible,
    form,
    modalVisible,
    tabKey,
    setTabKey,
    asyncReset,
  };
}

/* 转换DetailData 或者 initData */
function transDetail(inData: TypeOpenParams) {
  return {
    ...inData,
  };
}

export default {
  HBLInfo: {
    enclosure: '附件',
    theFirstEmail: '第{val1}个邮箱地址格式错误',
    notificationEmail: '通知邮箱',
    notifyThePersonsLocation: '通知人地址',
    notifyTheNameOfThePerson: '通知人名称',
    consigneesLocation: '收货人地址',
    recipientsName: '收货人名称',
    shippersLocation: '发货人地址',
    nameOfShipper: '发货人名称',
  },
  ModalPLDInitiateNotice: {
    singleInformation: 'H单信息',
    singleFile: 'M单文件',
    collectAndExchangeDocuments: '到付换单金额',
    collectOnDelivery: '到付',
    paymentInAdvance: '预付',
    exchangeFeePayment: '换单费付款方式',
    details: '详情id',
    saveAndSubmit: '保存并提交',
    preservation: '保存',
    cancel: '取消',
    confirmReset: '确定重置吗？',
    mustBeGreaterThan: '必须大于0',
  },
};

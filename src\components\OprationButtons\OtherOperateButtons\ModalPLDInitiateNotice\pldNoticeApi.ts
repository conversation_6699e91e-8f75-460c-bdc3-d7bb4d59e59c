import { requestHandler } from '@dzg/common-utils';

type TypeFileInfo = {
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 文件名 */
  fileName?: string;
  /** 文件大小 (bytes) */
  fileSize?: number;
  /** 文件类型: MBL(M单), HBL(H单) */
  fileType?: string;
  /** 文件存储路径 */
  fileUrl?: string;
  /** 主键ID */
  id?: number;
  /** 是否删除 0:否 1:是 */
  isDeleted?: boolean;
  /** 换单通知H单ID */
  notificationHblId?: number;
  /** 换单通知ID */
  notificationId?: number;
  /** 公司ID */
  orgId?: number;
  /** 集团公司ID */
  rootOrgId?: number;
  /** 上传人 */
  uploadBy?: string;
  /** 上传时间 */
  uploadTime?: string;
};

/** 换单通知详情 */
export type TypePLDNoticeDetail = {
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createTime?: string;
  /** 换单状态: 0(默认状态：未换单) 1(换单进行) 2(换单已完成) 3(换单已作废) */
  exchangeStatus?: number;
  /** 付款方式=到付,填写的金额 */
  feeCollectAmount?: number;
  /** 换单费付款方式: PREPAID(预付), COLLECT(到付) */
  feePaymentMethod?: string;
  /** 主键ID */
  id?: number;
  /** 是否删除 0:否 1:是 */
  isDeleted?: boolean;
  /** M单号, 前端传值 */
  mblNo?: string;
  /** H单信息列表 */
  hblList?: {
    /** 收货人地址 */ consigneeAddress?: string;
    /** 收货人名称 */
    consigneeName?: string;
    /** 创建人 */
    createBy?: string;
    /** 创建时间 */
    createTime?: string;
    /** 换单状态: 0(默认状态：未换单) 1(换单进行) 2(换单已完成) 3(换单已作废) */
    exchangeStatus?: number;
    /** H单文件信息列表 */
    hblFileList?: TypeFileInfo[];
    /** 源H单ID */
    hblId?: number;
    /** H单号 */
    hblNo?: string;
    /** 主键ID */
    id?: number;
    /** 是否删除 0:否 1:是 */
    isDeleted?: boolean;
    /** M单ID */
    mblId?: string;
    /** M单号 */
    mblNo?: string;
    /** 修改人 */
    modifyBy?: string;
    /** 修改时间 */
    modifyTime?: string;
    /** 通知邮箱 */
    notificationEmails?: string;
    /** 换单通知ID */
    notificationId?: number;
    /** 通知人地址 */
    notifierAddress?: string;
    /** 通知人名称 */
    notifierName?: string;
    /** 公司ID */
    orgId?: number;
    /** 集团公司ID */
    rootOrgId?: number;
    /** 发货人地址 */
    shipperAddress?: string;
    /** 发货人名称 */
    shipperName?: string;
    /** 通知状态: 0(未发送) 1(发送成功), 2(发送失败) */
    status?: number;
  }[];
  /** M单文件信息列表 */
  mblFileList?: TypeFileInfo[];
  /** 修改人 */
  modifyBy?: string;
  /** 修改时间 */
  modifyTime?: string;
  /** 订单ID */
  orderId?: string;
  /** 公司ID */
  orgId?: number;
  /** 目的港订单ID */
  podOrderId?: number;
  /** 集团公司ID */
  rootOrgId?: number;
  /** 通知状态: 0(未发送) 1(发送成功), 2(发送失败) */
  status?: number;
};

/** 获取换单通知详情 */
export function apiChangeOrderNoticeDetail(data: {
  /** 订单ID */
  orderId?: string;
}) {
  return requestHandler<TypePLDNoticeDetail>({
    url: `/gm2-fms-portal-server/bill-exchange/detail/${data.orderId}`,
    option: {
      method: 'GET',
      needLoad: true,
    },
  }).then(detailData => {
    const { mblFileList, hblList } = detailData;
    return {
      ...detailData,
      mblNo: hblList?.[0]?.mblNo,
      orderId: data.orderId,
      mblFileList: (mblFileList || []).map(item => ({
        ...item,
        url: item.fileUrl,
        name: item.fileName,
        status: 'done',
      })),
      hblList: (hblList || []).map(item => ({
        ...item,
        hblFileList: (item.hblFileList || []).map(file => ({
          ...file,
          url: file.fileUrl,
          name: file.fileName,
          status: 'done',
        })),
      })),
    };
  });
}

/** 保存换单通知 */
export function apiChangeOrderSave(data: TypePLDNoticeDetail) {
  const { mblNo, hblList } = data;
  /** 判断值是否变化, 变化时mblId清空 */
  const isMblNoChange = hblList?.[0]?.mblNo !== mblNo;
  return requestHandler({
    url: '/gm2-fms-portal-server/bill-exchange/save',
    option: {
      method: 'POST',
      body: JSON.stringify({
        ...data,
        hblList: (hblList || []).map(item => ({
          ...item,
          mblId: isMblNoChange ? null : item.mblId,
          mblNo,
        })),
      }),
      needLoad: true,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
      },
    },
  });
}
/** 重置换单信息 */
export function apiReBillExchange(notificationId: string) {
  return requestHandler({
    url: `/gm2-fms-portal-server/bill-exchange/reBillExchange/${notificationId}`,
    option: {
      method: 'GET',
      needLoad: true,
    },
  });
}

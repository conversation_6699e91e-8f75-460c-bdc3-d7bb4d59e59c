import I18N from '@/utils/I18N';
import { validatorHelper } from '@dzg/common-utils';
import Form from '@/components/EnhanceForm';
import { FormInstance, Input } from 'antd';
import React from 'react';
import { OssFileUploader } from '@dzg/base-component';

type TypeProps = {
  /** 组件Props定义 */
  namePathPrefix: string | number;
  form: FormInstance;
};

/** H单信息 */
export function HBLInfo(props: TypeProps) {
  const { namePathPrefix } = props;

  return (
    <div>
      <Form.Item
        {...{
          name: [namePathPrefix, 'shipperName'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo.nameOfShipper,
          rules: [{ required: true }, validatorHelper({ max: 70 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'shipperAddress'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo.shippersLocation,
          rules: [{ required: true }, validatorHelper({ max: 255 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'consigneeName'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo.recipientsName,
          rules: [{ required: true }, validatorHelper({ max: 70 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'consigneeAddress'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo
              .consigneesLocation,
          rules: [{ required: true }, validatorHelper({ max: 255 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'notifierName'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo
              .notifyTheNameOfThePerson,
          rules: [{ required: true }, validatorHelper({ max: 70 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'notifierAddress'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo
              .notifyThePersonsLocation,
          rules: [{ required: true }, validatorHelper({ max: 255 })],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'notificationEmails'],
          label:
            I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo
              .notificationEmail,
          rules: [
            { required: true },
            validatorHelper({ max: 150 }),
            {
              async validator(rule, value) {
                const arr = value.split(',');
                arr.forEach((item: any, index: number) => {
                  if (!item || !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(item)) {
                    throw new Error(
                      I18N.template(
                        I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo
                          .theFirstEmail,
                        { val1: index + 1 }
                      )
                    );
                  }
                });
              },
            },
          ],
          children: <Input />,
        }}
      />
      <Form.Item
        {...{
          name: [namePathPrefix, 'hblFileList'],
          label: I18N.Src__Components__OprationButtons__OtherOperateButtons__ModalPLDInitiateNotice.HBLInfo.enclosure,
          rules: [{ required: true }],
          valuePropName: 'fileList',
          wrapperCol: {
            span: 17,
          },
          children: (
            <OssFileUploader
              {...{
                maxFileSizeM: 100,
                maxFileCount: 1,
                isDragger: false,
                enableSuffixList: '.doc,.docx,.pdf,.xls,.xlsx,.jpg,.jpeg,.png',
                onChange(fileList) {
                  (fileList || []).forEach((item: any) => {
                    if (item.status === 'done') {
                      item.fileName = item.name;
                      item.fileUrl = item.url;
                      item.fileType = 'HBL';
                      item.fileSize = item.size;
                    }
                  });
                },
              }}
            />
          ),
        }}
      />
    </div>
  );
}
